<template>
  <div class="test-container">
    <h2>JSON编辑器宽高测试</h2>
    
    <div class="test-box">
      <h3>固定宽高容器 (600x400)</h3>
      <div class="fixed-container">
        <JsonEditor
          v-model="testData"
          :height="'100%'"
          title="固定容器测试"
          :mode="'code'"
        />
      </div>
    </div>

    <div class="test-box">
      <h3>弹性容器</h3>
      <div class="flex-container">
        <JsonEditor
          v-model="testData2"
          :height="'300px'"
          title="弹性容器测试"
          :mode="'tree'"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import JsonEditor from '@/components/common/JsonEditor/index.vue';

const testData = ref({
  "groupBy": "TEAMS",
  "chartType": "line",
  "colors": ["#1890ff", "#52c41a", "#faad14"],
  "animation": {
    "duration": 1000,
    "easing": "easeInOut"
  },
  "responsive": true,
  "maintainAspectRatio": false
});

const testData2 = ref({
  "configuration": {
    "groupBy": "NONE", 
    "showLegend": true,
    "responsive": true
  }
});
</script>

<style lang="less" scoped>
.test-container {
  padding: 20px;
  
  .test-box {
    margin-bottom: 30px;
    
    h3 {
      margin-bottom: 10px;
      color: #333;
    }
  }
  
  .fixed-container {
    width: 600px;
    height: 400px;
    border: 2px solid #1890ff;
    border-radius: 8px;
  }
  
  .flex-container {
    width: 100%;
    max-width: 800px;
    border: 2px solid #52c41a;
    border-radius: 8px;
  }
}
</style>
